'use server'
import { AliyunCredentials } from '../../../bot/lib/cer'
import { FreeSpiritOss } from '../../../bot/model/oss/oss'


export async function putObjectStream(name: string, file: File, basePath:string = 'rag_file'):Promise<string> {
  const bucket = new FreeSpiritOss('static')

  const buffer = await file.arrayBuffer()
  const readableBuffer = Buffer.from(buffer)
  const res = await bucket.putObject(`${basePath}/${name}`, readableBuffer)
  return res.url
}

AliyunCredentials.initialize({
  region: 'cn-hangzhou',
  accountId: '****************',
  accessKeyId: 'LTAI5tRVPxefUtgyLCfc5f69',
  secretAccessKey: '******************************',
})