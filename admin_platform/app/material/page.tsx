'use client'

import { useState } from 'react'
import MaterialShow from '../component/material/materialShow'
import { Material, queryMaterials, getMaterialTypes, updateMaterial, getMainCategories, getSubCategories, deleteMaterial, toggleMaterialEnable, getDocOptions } from '@/app/api/material'

const MATERIAL_TYPES = {
  101: '文本',
  102: '图片',
  103: '视频',
  104: '文件',
  105: '音频',
  106: '链接小卡片',
  107: '视频号',
}

const DOC_OPTIONS = ['全局', '售前', '售中', '售后']

export default function MaterialPage() {
  const getPreviewContent = (material: Material, handlePreview: (material: Material) => void) => {
    const contentData = material.data as any
    switch (material.type) {
      default:
        return null
    }
  }

  const renderPreviewContent = (material: Material) => {
    const contentData = material.data as any
    switch (material.type) {
      default:
        return (
          <div className="bg-base-100 p-4 rounded-lg">
            <h3 className="font-bold mb-2">原始数据：</h3>
            <pre className="text-xs bg-gray-100 p-2 rounded overflow-auto max-h-96">
              {JSON.stringify(contentData, null, 2)}
            </pre>
          </div>
        )
    }
  }

  return (
    <div>
      <MaterialShow
        queryMaterials={queryMaterials}
        getMaterialTypes={getMaterialTypes}
        updateMaterial={updateMaterial}
        getMainCategories={getMainCategories}
        getSubCategories={getSubCategories}
        deleteMaterial={deleteMaterial}
        toggleMaterialEnable={toggleMaterialEnable}
        getDocOptions={getDocOptions}
        getPreviewContent={getPreviewContent}
        renderPreviewContent={renderPreviewContent}
        MATERIAL_TYPES={MATERIAL_TYPES}
        DOC_OPTIONS={DOC_OPTIONS}
      />
    </div>
  )
}