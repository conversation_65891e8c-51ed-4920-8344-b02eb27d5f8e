'use server'

import { PrismaMongoClient } from '../../../bot/model/mongodb/prisma'

export interface Material {
  id: string
  type: number
  title: string
  description: string
  main_category: string
  sub_category: string
  doc: string
  data: any
  enable: boolean
  es_id: string
  created_at?: Date
}

export interface MaterialQueryParams {
  page?: number
  pageSize?: number
  type?: number
  main_category?: string
  sub_category?: string
  doc?: string
}

export async function queryMaterials(params: MaterialQueryParams = {}) {
  const { page = 1, pageSize = 20, type, main_category, sub_category, doc } = params
  const skip = (page - 1) * pageSize

  try {
    const whereCondition: any = {}
    if (type !== undefined) {
      whereCondition.type = type
    }
    if (main_category) {
      whereCondition.main_category = main_category
    }
    if (sub_category) {
      whereCondition.sub_category = sub_category
    }
    if (doc) {
      whereCondition.doc = doc
    }

    const [materials, total] = await Promise.all([
      PrismaMongoClient.getInstance().material.findMany({
        where: whereCondition,
        skip,
        take: pageSize,
        orderBy: {
          id: 'desc'  // 使用id排序代替created_at
        }
      }),
      PrismaMongoClient.getInstance().material.count({
        where: whereCondition
      })
    ])

    return {
      materials,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize)
    }
  } catch (error) {
    console.error('Error querying materials:', error)
    throw error
  }
}

export async function updateMaterial(id: string, updates: { title?: string; description?: string; doc?: string; main_category?: string; sub_category?: string }) {
  try {
    const material = await PrismaMongoClient.getInstance().material.update({
      where: { id },
      data: updates
    })
    return material
  } catch (error) {
    console.error('Error updating material:', error)
    throw error
  }
}

export async function toggleMaterialEnable(id: string, enable: boolean) {
  try {
    const updateData: any = { enable }

    if (!enable) {
      // 禁用素材时清空es_id
      updateData.es_id = ''
    }

    const material = await PrismaMongoClient.getInstance().material.update({
      where: { id },
      data: updateData
    })

    return material
  } catch (error) {
    console.error('Error toggling material enable:', error)
    throw error
  }
}

export async function getMaterialTypes() {
  try {
    const materials = await PrismaMongoClient.getInstance().material.groupBy({
      by: ['type'],
      _count: {
        type: true
      }
    })

    return materials.map((item) => ({
      type: item.type,
      count: item._count.type
    }))
  } catch (error) {
    console.error('Error getting material types:', error)
    throw error
  }
}

export async function getMainCategories() {
  try {
    const materials = await PrismaMongoClient.getInstance().material.groupBy({
      by: ['main_category'],
      _count: {
        main_category: true
      }
    })

    return materials.map((item) => ({
      main_category: item.main_category,
      count: item._count.main_category
    })).sort((a, b) => b.count - a.count)
  } catch (error) {
    console.error('Error getting main categories:', error)
    throw error
  }
}

export async function getSubCategories(mainCategory?: string) {
  try {
    const whereCondition: any = {}
    if (mainCategory) {
      whereCondition.main_category = mainCategory
    }

    const materials = await PrismaMongoClient.getInstance().material.groupBy({
      by: ['sub_category'],
      where: whereCondition,
      _count: {
        sub_category: true
      }
    })

    return materials.map((item) => ({
      sub_category: item.sub_category,
      count: item._count.sub_category
    })).sort((a, b) => b.count - a.count) // 按使用频率排序
  } catch (error) {
    console.error('Error getting sub categories:', error)
    throw error
  }
}

export async function deleteMaterial(id: string) {
  try {
    await PrismaMongoClient.getInstance().material.delete({
      where: { id }
    })
  } catch (error) {
    console.error('Error deleting material:', error)
    throw error
  }
}

export async function getDocOptions() {
  try {
    const materials = await PrismaMongoClient.getInstance().material.groupBy({
      by: ['doc'],
      _count: {
        doc: true
      }
    })

    return materials.map((item) => ({
      doc: item.doc,
      count: item._count.doc
    })).sort((a, b) => b.count - a.count)
  } catch (error) {
    console.error('Error getting doc options:', error)
    throw error
  }
}

export async function checkMaterialsExistence(ids: string[]) {
  try {
    const results: Record<string, boolean> = {}

    // 批量查询已存在的素材
    const existingMaterials = await PrismaMongoClient.getInstance().material.findMany({
      where: {
        id: {
          in: ids
        }
      },
      select: {
        id: true
      }
    })

    // 构建存在状态映射
    const existingIds = new Set(existingMaterials.map((m) => m.id))
    ids.forEach((id) => {
      results[id] = existingIds.has(id)
    })

    return results
  } catch (error) {
    console.error('Error checking materials existence:', error)
    throw error
  }
}

export async function createMaterial(data: {
  type: number
  title: string
  description: string
  main_category: string
  sub_category: string
  data: any
  doc?: string
}) {
  try {
    const material = await PrismaMongoClient.getInstance().material.create({
      data: {
        type: data.type,
        title: data.title,
        description: data.description,
        main_category: data.main_category,
        sub_category: data.sub_category,
        doc: data.doc || '',
        data: data.data,
        enable: true,
        es_id: ''
      }
    })
    return material
  } catch (error) {
    console.error('Error creating material:', error)
    throw error
  }
}